/**
 * User Auth Service
 *
 * Contains business logic for user authentication
 */
const jwt = require('jsonwebtoken');
const { ApiException } = require('@utils/exception.utils');
const userRepository = require('@models/repositories/user.repository');
const { AUTH } = require('@utils/messages.utils');
const { UserRole, UserType, HttpStatus } = require('@utils/enums.utils');
const databaseService = require('@config/database.config');
const { VALIDATION } = require('@utils/messages.utils');

/**
 * User auth service
 */
const authService = {
  /**
   * Generate JWT token
   * @param {Object} payload - Data to be included in token
   * @returns {string} JWT token
   */
  generateToken: (payload) => {
    try {
      return jwt.sign(payload, process.env.JWT_SECRET);
    } catch (error) {
      console.error('Error in generateToken service:', error);
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        AUTH.TOKEN_GENERATION_FAILED
      );
    }
  },

  /**
   * Verify JWT token
   * @param {string} token - JWT token to verify
   * @returns {Object} Decoded token payload
   */
  verifyToken: (token) => {
    try {
      return jwt.verify(token, process.env.JWT_SECRET);
    } catch (error) {
      console.error('Error in verifyToken service:', error);
      throw new ApiException(HttpStatus.UNAUTHORIZED, AUTH.INVALID_TOKEN);
    }
  },

  /**
   * Register a new user
   * @param {Object} userData - User registration data
   * @returns {Object} User data and token
   */
  registerUser: async (userData) => {
    try {
      const { email, userType } = userData;

      // Check if user already exists
      const existingUser = await userRepository.findByEmail(email);
      if (existingUser) {
        throw new ApiException(HttpStatus.CONFLICT, AUTH.EMAIL_ALREADY_EXISTS);
      }

      // Validate user type
      if (!UserType.isValid(userType)) {
        throw new ApiException(HttpStatus.BAD_REQUEST, AUTH.INVALID_USER_TYPE);
      }

      // Create user with focus associations
      const user = await userRepository.createUserWithFocus(userData);

      // Generate token
      const token = jwt.sign(
        {
          id: user.id,
          email: user.email,
          userType: user.userType,
          role: UserRole.USER,
        },
        process.env.JWT_SECRET
      );

      // Return user data (without password)
      return {
        user: {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          userType: user.userType,
          position: user.position,
          state: user.state,
          country: user.country,
          companyName: user.companyName,
          focuses: user.focuses,
          role: UserRole.USER,
        },
        token,
      };
    } catch (error) {
      console.error('Error in registerUser service:', error);
      // If it's already an ApiException, just rethrow it
      if (error instanceof ApiException) {
        throw error;
      }
      // For other errors, wrap them in an ApiException with a generic message
      throw new ApiException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        AUTH.REGISTRATION_FAILED,
        error.message
      );
    }
  },

  /**
   * Login user
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Object} User data and token
   */
  loginUser: async (email, password) => {
    try {
      // Find user
      const user = await userRepository.findByEmail(email);

      if (!user) {
        throw new ApiException(HttpStatus.UNAUTHORIZED, AUTH.EMAIL_NOT_FOUND);
      }

      // Verify password
      const isPasswordValid = await user.verifyPassword(password);

      if (!isPasswordValid) {
        throw new ApiException(
          HttpStatus.UNAUTHORIZED,
          AUTH.INVALID_CREDENTIALS
        );
      }

      // Generate token (without expiration)
      const token = jwt.sign(
        {
          id: user.id,
          email: user.email,
          userType: user.userType,
          role: UserRole.USER,
        },
        process.env.JWT_SECRET
      );

      // Return user data with token
      return {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        userType: user.userType,
        position: user.position,
        focus: user.focus,
        state: user.state,
        country: user.country,
        companyName: user.companyName,
        profilePic: user.profilePic,
        role: UserRole.USER,
        token: token,
      };
    } catch (error) {
      // Log the error for debugging
      console.error('Error in loginUser service:', error);
      throw error;
    }
  },

  /**
   * Get user profile
   * @param {string} userId - User ID
   * @returns {Object} User profile data
   */
  getUserProfile: async (userId) => {
    try {
      return await userRepository.getUserProfileData(userId);
    } catch (error) {
      console.error('Error in getUserProfile service:', error);
      throw error;
    }
  },

  /**
   * Update user profile
   * @param {string} userId - User ID
   * @param {Object} profileData - Updated profile data
   * @returns {Object} Updated user profile data
   */
  updateUserProfile: async (userId, profileData) => {
    try {
      // Update user profile using repository
      return await userRepository.updateProfile(userId, profileData);
    } catch (error) {
      console.error('Error in updateUserProfile service:', error);
      throw error;
    }
  },
};

module.exports = authService;
