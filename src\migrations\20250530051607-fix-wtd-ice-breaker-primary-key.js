'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // First, check if there are any duplicate userId records
      const duplicates = await queryInterface.sequelize.query(
        `
        SELECT "userId", COUNT(*) as count 
        FROM "WtdIceBreaker" 
        GROUP BY "userId" 
        HAVING COUNT(*) > 1
        `,
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );

      // If there are duplicates, keep only the most recent one for each user
      if (duplicates.length > 0) {
        console.log(
          `Found ${duplicates.length} users with duplicate ice breaker records. Cleaning up...`
        );

        for (const duplicate of duplicates) {
          // Keep only the most recent record for each user
          await queryInterface.sequelize.query(
            `
            DELETE FROM "WtdIceBreaker" 
            WHERE "userId" = :userId 
            AND id NOT IN (
              SELECT id FROM "WtdIceBreaker" 
              WHERE "userId" = :userId 
              ORDER BY "updatedAt" DESC 
              LIMIT 1
            )
            `,
            {
              replacements: { userId: duplicate.userId },
              type: Sequelize.QueryTypes.DELETE,
              transaction,
            }
          );
        }
      }

      // Drop the existing primary key constraint
      await queryInterface.removeConstraint(
        'WtdIceBreaker',
        'WtdIceBreaker_pkey',
        { transaction }
      );

      // Drop the existing index on userId (we'll recreate it as unique)
      await queryInterface.removeIndex('WtdIceBreaker', ['userId'], {
        transaction,
      });

      // Add a new UUID column for the new primary key
      await queryInterface.addColumn(
        'WtdIceBreaker',
        'uuid_id',
        {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          allowNull: false,
        },
        { transaction }
      );

      // Update all existing records to have UUID values
      await queryInterface.sequelize.query(
        'UPDATE "WtdIceBreaker" SET uuid_id = gen_random_uuid() WHERE uuid_id IS NULL',
        { transaction }
      );

      // Drop the old integer id column
      await queryInterface.removeColumn('WtdIceBreaker', 'id', { transaction });

      // Rename the uuid_id column to id
      await queryInterface.renameColumn('WtdIceBreaker', 'uuid_id', 'id', {
        transaction,
      });

      // Add the new primary key constraint
      await queryInterface.addConstraint(
        'WtdIceBreaker',
        {
          fields: ['id'],
          type: 'primary key',
          name: 'WtdIceBreaker_pkey',
        },
        { transaction }
      );

      // Add unique constraint on userId
      await queryInterface.addConstraint(
        'WtdIceBreaker',
        {
          fields: ['userId'],
          type: 'unique',
          name: 'wtd_ice_breaker_user_id_unique_idx',
        },
        { transaction }
      );

      await transaction.commit();
      console.log(
        'Successfully migrated WtdIceBreaker table to use UUID primary key and unique userId constraint'
      );
    } catch (error) {
      await transaction.rollback();
      console.error('Error migrating WtdIceBreaker table:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Remove the unique constraint on userId
      await queryInterface.removeConstraint(
        'WtdIceBreaker',
        'wtd_ice_breaker_user_id_unique_idx',
        { transaction }
      );

      // Remove the primary key constraint
      await queryInterface.removeConstraint(
        'WtdIceBreaker',
        'WtdIceBreaker_pkey',
        { transaction }
      );

      // Rename id column to uuid_id temporarily
      await queryInterface.renameColumn('WtdIceBreaker', 'id', 'uuid_id', {
        transaction,
      });

      // Add back the integer id column with auto-increment
      await queryInterface.addColumn(
        'WtdIceBreaker',
        'id',
        {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false,
        },
        { transaction }
      );

      // Remove the UUID column
      await queryInterface.removeColumn('WtdIceBreaker', 'uuid_id', {
        transaction,
      });

      // Add back the index on userId (non-unique)
      await queryInterface.addIndex('WtdIceBreaker', ['userId'], {
        transaction,
      });

      await transaction.commit();
      console.log(
        'Successfully reverted WtdIceBreaker table to use INTEGER primary key'
      );
    } catch (error) {
      await transaction.rollback();
      console.error('Error reverting WtdIceBreaker table:', error);
      throw error;
    }
  },
};
